---
type: "agent_requested"
description: "Development Rules Index"
---

# Development Rules Index

## Component Rules
- **Server Components Only**: Components must be server components ("use client" must not be present)
- **Location**: All components live under the `app/` directory
- **Export Pattern**: Use default export for page components
- **Styling**: Import styles with module CSS or Tailwind (not global CSS)
- **File Naming**: Use PascalCase for filenames (e.g., `Navbar.tsx`)
- **Props Interface**: Define props interfaces with the name `<ComponentName>Props`
- **Function Components**: Use TypeScript function components with explicit return types
- **Component Exports**: Export components as named functions, not defaults

## API Route Rules
- **Location**: Place API routes under `app/api/{route}/route.ts`
- **HTTP Methods**: Export functions for supported HTTP methods (GET, POST, etc.)
- **Validation**: Always validate request data with Zod
- **Responses**: Return JSON responses with appropriate status codes

## Routing Rules
- **App Router Only**: All routes must be inside the `app/` directory
- **No Pages Directory**: The legacy `pages/` directory must not be used
- **Entry Points**: Each route directory must contain a `page.tsx` entry point

## Import Rules
- **Absolute Imports**: Always use absolute imports from the `@/` alias in `tsconfig.json`
- **No Relative Imports**: Never use relative imports like `../../components/...`
- **API Helper**: Use the `lib/api.ts` helper for all API calls

## TypeScript Rules
- **File Extensions**: All new files must use `.ts` or `.tsx` extensions
- **Props Typing**: Props must always have an explicit TypeScript interface or type alias
- **Return Types**: Return types must be declared for all functions
- **ESLint Compliance**: ESLint rules must be followed

## Styling Rules
- **Tailwind Only**: All components must use Tailwind CSS for styling
- **No Inline Styles**: Avoid inline styles and global CSS imports
- **Semantic HTML**: Use semantic HTML elements where possible

## Layout Rules
- **Required Files**: Every route directory must contain both `page.tsx` and `layout.tsx`
- **Layout Structure**: Layout files must wrap children with a `<section>` element
- **Spacing**: Use Tailwind classes for layout spacing

## Quick Reference Checklist
- [ ] Server component (no "use client")
- [ ] Located in `app/` directory
- [ ] Default export for pages
- [ ] PascalCase filename
- [ ] `<ComponentName>Props` interface
- [ ] Explicit return types
- [ ] Absolute imports with `@/`
- [ ] Tailwind CSS styling
- [ ] TypeScript with proper typing
- [ ] ESLint compliant